import { checkPlacementLogic } from './check-placement-logic.mjs';
import { logger } from './logger.mjs';

export async function runPlacementCheck(
  page,
  adsToCheck,
  deviceName,
  responsiveKey,
  issues
) {
  const unplacedAds = await page.evaluate(checkPlacementLogic, adsToCheck);
  if (unplacedAds.length > 0) {
    if (!issues.placement)
      issues.placement = { desktop: [], tablet: [], mobile: [] };
    issues.placement[responsiveKey] = unplacedAds;
    logger.silly(
      `[${deviceName}] ERROR [Placement]: ${unplacedAds.length} Display containers not found in the DOM:`,
      unplacedAds.map((ad) => ad.id)
    );
    console.log(
      `[${deviceName}] ERROR [Placement]: ${unplacedAds.length} Display containers not found in the DOM:`,
      unplacedAds.map((ad) => ad.id)
    );
  } else {
    logger.silly(
      `[${deviceName}] SUCCESS [Placement]: All ${adsToCheck.length} display containers found.`
    );
    console.log(
      `[${deviceName}] SUCCESS [Placement]: All ${adsToCheck.length} display containers found.`
    );
  }
  return unplacedAds.length;
}
