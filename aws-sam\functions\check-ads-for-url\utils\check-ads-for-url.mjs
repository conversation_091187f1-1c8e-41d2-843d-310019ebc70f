import { viewports } from './viewports.mjs';
import { runChecksForDevice } from './run-checks-for-device.mjs';
import { logger } from './logger.mjs';
import { formatMilliseconds } from './format-milliseconds.mjs';
import pushFlag from './push-flag.mjs';
import severityFromPercentage from './severity-from-percentage.mjs';
import calculateProblemPercentage from './calculate-problem-percentage.mjs';


export async function checkAdsForUrl(browser, params) {
  const { url, articleId, layers } = params;
  const activeLayers = layers || ['placement', 'visibility'];

  logger.info('Job started', { articleId: articleId });
  console.log('Job started', { articleId: articleId })

  logger.silly(
    `📥 Start analysis: ${url} with layers [${activeLayers.join(', ')}]`
  );
  console.log(`📥 Start analysis: ${url} with layers [${activeLayers.join(', ')}]`)

  const startTime = performance.now();
  let durationMs = 0;

  try {
    const consentState = { handled: false }; // Flag to track if consent has been handled.

    const deviceResults = [];
    // Run checks sequentially to handle consent state correctly
    for (const [deviceName, config] of Object.entries(viewports)) {
      const result = await runChecksForDevice(
        browser,
        url,
        deviceName,
        config,
        activeLayers,
        consentState
      );
      deviceResults.push(result);
    }

    const flags = [];
    
    // Get adsByResponsive from the first device result (it's the same for all devices)
    const adsByResponsive = deviceResults.length > 0 ? deviceResults[0].adsByResponsive : {};

    for (const result of deviceResults) {
      const responsiveKey = result.responsiveKey;

      if (result.error) {
        const msg = String(result.error);
        if (msg.includes('Cookie consent could not be accepted')) {
          pushFlag({
            issue: 'Cookie consent fails',
            description: null,
            severity: 'RED',
            step: 'Page Opening',
            device: result.deviceName,
            responsive: responsiveKey,
          },flags);
        } else if (msg.includes('TCF validation failed')) {
          pushFlag({
            issue: 'TCF Validation fails',
            description: null,
            severity: 'RED',
            step: 'Page Opening',
            device: result.deviceName,
            responsive: responsiveKey,
          },flags);
        } else if (msg.includes('Could not retrieve window.qmn.config object')) {
          pushFlag({
            issue: 'No qmn object on website',
            description: null,
            severity: 'RED',
            step: 'Check Validity',
            device: result.deviceName,
            responsive: responsiveKey,
          },flags);
        }
      }

 // Placement layer -> qmn div container not found
      if (result.issues && result.issues.placement && result.issues.placement[responsiveKey]) {
        for (const ad of result.issues.placement[responsiveKey]) {
          pushFlag({
            issue: 'qmn div container not found',
            description: 'Cannot check the Ad if no qmn[div] container',
            severity: 'RED',
            step: 'Check Existence',
            device: result.deviceName,
            responsive: responsiveKey,
            adId: ad.id,
            adType: ad.type,
          },flags);
        }
      }

      // Visibility layer mappings
      if (result.issues && result.issues.visibility && result.issues.visibility[responsiveKey]) {
        for (const ad of result.issues.visibility[responsiveKey]) {
          const visibilityDetails = ad.visibilityDetails || {};
          const vis = typeof visibilityDetails.visibilityPercentage === 'number' ? visibilityDetails.visibilityPercentage : null;
          const reason = visibilityDetails.reason || '';

          // Specific reasons mapping
          if (reason.startsWith('No container div')) {
            pushFlag({
              issue: 'qmn div container not found',
              description: 'Cannot check the Ad if no qmn[div] container',
              severity: 'RED',
              step: 'Check Visibility',
              device: result.deviceName,
              responsive: responsiveKey,
              adId: ad.id,
              adType: ad.type,
            },flags);
            continue;
          }
          if (reason.includes('Iframe element not found')) {
            pushFlag({
              issue: 'iframe missing',
              description: 'Cannot check the Ad if no iframe',
              severity: 'RED',
              step: 'Check Visibility',
              device: result.deviceName,
              responsive: responsiveKey,
              adId: ad.id,
              adType: ad.type,
            },flags);
            continue;
          }
          if (reason === 'Element not found') {
            pushFlag({
              issue: 'Bounding box not found',
              description: 'Element no found when trying to get boundingRect',
              severity: 'RED',
              step: 'Check Visibility',
              device: result.deviceName,
              responsive: responsiveKey,
              adId: ad.id,
              adType: ad.type,
            },flags);
            continue;
          }
          if (reason === 'Hidden by CSS' || visibilityDetails.isVisibleByCss === false) {
            pushFlag({
              issue: 'Hidden by CSS',
              description: 'Ad hidden by CSS properties.',
              severity: 'RED',
              step: 'Check Visibility',
              device: result.deviceName,
              responsive: responsiveKey,
              adId: ad.id,
              adType: ad.type,
            },flags);
            continue;
          }
          if (reason === 'Not within page bounds' || visibilityDetails.isWithinPageBounds === false) {
            pushFlag({
              issue: 'Not within PageBound',
              description: 'Ad not within the page',
              severity: 'RED',
              step: 'Check Visibility',
              device: result.deviceName,
              responsive: responsiveKey,
              adId: ad.id,
              adType: ad.type,
            },flags);
            continue;
          }

          if (visibilityDetails.isPartiallyOutOfViewport) {
            const sev = severityFromPercentage(vis);
            if (sev) {
              pushFlag({
                issue: 'Ad not fully in viewport',
                description: 'Partially / completely out of viewport',
                visibilityPercentage: vis,
                severity: sev,
                step: 'Check Visibility',
                device: result.deviceName,
                responsive: responsiveKey,
                adId: ad.id,
                adType: ad.type,
              },flags);
              continue;
            }
          }

          if (visibilityDetails.isOccluded) {
            const sev = severityFromPercentage(vis);
            if (sev) {
              pushFlag({
                issue: 'Ad is covered',
                description: 'Ad covered by an other element',
                visibilityPercentage: vis,
                severity: sev,
                step: 'Check Visibility',
                device: result.deviceName,
                responsive: responsiveKey,
                adId: ad.id,
                adType: ad.type,
                occluders: visibilityDetails.occludingElements,
              },flags);
              continue;
            }
          }
        }
      }
    }

    const endTime = performance.now();
    durationMs = endTime - startTime;

    // Calculate problem percentage
    const problemPercentage = calculateProblemPercentage(flags, adsByResponsive);

    let finalResult = {
      id: articleId.toString(),
      success: flags.length === 0,
      url,
      checkedLayers: activeLayers,
      timestamp: new Date().toISOString(),
      processingTimeMs: durationMs,
      flags,
      problemPercentage: problemPercentage,
    };

    logger.silly('final result 📈 :', JSON.stringify(finalResult, null, 2));
    await browser.close();

    return finalResult;
  } catch (e) {
    logger.error(
      `❌ Critical error during job execution for ${url}:`,
      e.message
    );
    return {
      id: articleId.toString(),
      url,
      error: e.message,
      timestamp: new Date().toISOString(),
    };
  } finally {
    logger.info(`Job completed in ${formatMilliseconds(durationMs)}`);
  }
}