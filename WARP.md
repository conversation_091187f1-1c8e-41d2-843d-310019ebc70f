# WARP.md

This file provides guidance to <PERSON><PERSON> (warp.dev) when working with code in this repository.

## Project Overview

Ad-checker is a BullMQ worker that processes URL-checking jobs from Redis and analyzes advertisements using Puppeteer. The project has dual deployment modes:
1. **BullMQ Worker Mode**: Runs as a containerized service consuming jobs from Redis
2. **AWS Lambda Mode**: Deployed via AWS SAM for serverless execution

## Architecture

### Core Components

- **BullMQ Worker** (`src/index.js`): Main entry point that connects to Redis and processes jobs
- **Job Handler** (`src/handler.js`): Routes different job types to appropriate handlers
- **Ad Check Logic** (`src/handle-ad-check.js`): Orchestrates Puppeteer browser automation
- **AWS Lambda Function** (`aws-sam/functions/check-ads-for-url/app.mjs`): Lambda handler for serverless execution
- **Core Analysis Engine** (`aws-sam/functions/check-ads-for-url/utils/check-ads-for-url.mjs`): Main ad analysis logic

### Analysis Layers

The system supports two analysis layers configurable via the `layers` parameter:
- **placement**: Checks if ad containers (qmn div elements) exist
- **visibility**: Analyzes ad visibility including viewport positioning, CSS visibility, and occlusion

### Multi-Device Testing

The analysis runs across different viewport configurations defined in `viewports.mjs` to test responsive behavior.

## Common Commands

### Development

```bash
# Install dependencies
npm install

# Lint code
npm run lint

# Fix linting issues automatically  
npm run lint:fix

# Format code
npm run format
```

### Docker Development

```bash
# Build and run with Docker Compose
docker-compose up --build

# Scale worker instances
docker-compose up --scale worker=3

# View logs
docker-compose logs -f worker
```

### AWS SAM Deployment

```bash
# Build SAM application
sam build --use-container

# Deploy to AWS (using samconfig.toml settings)
sam deploy

# Local testing
sam local invoke CheckAdsForURL --event event.json

# Generate sample events for testing
sam local generate-event apigateway aws-proxy > event.json
```

### Testing Individual Functions

```bash
# Test the main handler directly (modify params in handle-ad-check.js first)
node src/handle-ad-check.js
```

## Environment Configuration

### Required Environment Variables

**BullMQ Worker Mode:**
- `REDIS_HOST`: Redis server hostname
- `REDIS_PORT`: Redis server port
- `REDIS_USERNAME`: Redis username  
- `REDIS_PASSWORD`: Redis password
- `BULLMQ_CONCURRENCY`: Number of concurrent jobs (default: 1)
- `NODE_ENV`: Environment (affects Redis TLS usage)
- `PUPPETEER_EXECUTABLE_PATH`: Custom Chromium path (optional)

**AWS Lambda Mode:**
- Uses @sparticuz/chromium for serverless Chromium
- No Redis configuration needed

## Key Implementation Details

### Job Structure

Jobs should contain:
```json
{
  "url": "https://example.com",
  "articleId": "12345", 
  "layers": ["placement", "visibility"]
}
```

### Browser Configuration

- **Docker/Local**: Uses system Chromium with `--no-sandbox` args
- **AWS Lambda**: Uses @sparticuz/chromium layer for serverless compatibility
- **Headless Mode**: Configurable via headless parameter

### Cookie Consent Handling

The system includes automatic cookie consent handling with state tracking across devices to avoid duplicate consent interactions.

### Error Handling Categories

The analysis categorizes issues into:
- Cookie consent failures
- TCF validation failures  
- Missing QMN object
- Container/iframe not found
- CSS visibility issues
- Viewport positioning problems
- Element occlusion

## File Structure Notes

- `src/`: BullMQ worker implementation
- `aws-sam/`: AWS SAM serverless deployment
- `aws-sam/functions/check-ads-for-url/utils/`: Core analysis utilities shared between modes
- `result*.json`: Sample output files from test runs
- Docker configuration supports multi-replica deployment

## Development Tips

- The core ad analysis logic in `utils/check-ads-for-url.mjs` is shared between BullMQ and Lambda modes
- Modify viewport configurations in `viewports.mjs` to test different device scenarios  
- Use the result*.json files as examples of expected output structure
- The system handles sequential device testing to properly manage cookie consent state