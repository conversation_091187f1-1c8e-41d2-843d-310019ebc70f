import puppeteer from 'puppeteer';
import { checkAdsForUrl } from '../aws-sam/functions/check-ads-for-url/utils/check-ads-for-url.mjs';

export async function handleAdCheck(params) {
  const browser = await puppeteer.launch({
    executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
    headless: "new", // Use 'new' for headless mode
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  return checkAdsForUrl(browser, params);
}