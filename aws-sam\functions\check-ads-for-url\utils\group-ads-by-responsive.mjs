import { logger } from './logger.mjs';

export const groupAdsByResponsive = (qmn) => {
  if (!qmn || !qmn.adSlots || !Array.isArray(qmn.adSlots)) {
    logger.silly('QMN object or qmn.adSlots is not in the expected format.');
    return {};
  }

  const acc = qmn.adSlots.reduce((acc, adSlot) => {
    const { id, responsive, type } = adSlot;

    // Ignore ads of type 'tracker' and slots without ID/responsive.
    if (!id || !responsive || type === 'tracker') {
      return acc;
    }

    // Make sure the array for the device type exists.
    if (!acc[responsive]) {
      acc[responsive] = [];
    }

    // Add display object to list.
    acc[responsive].push({ id, type });

    return acc;
  }, {});

  // Ensure all device type arrays exist
  if (!acc.mobile) acc.mobile = [];
  if (!acc.tablet) acc.tablet = [];
  if (!acc.desktop) acc.desktop = [];

  // Copy desktop ads to tablet as well (tablets often behave like desktops)
  if (acc.desktop && acc.desktop.length > 0) {
    acc.tablet.push(...acc.desktop);
  }

  if (qmn.watchbetter) {
    acc.mobile.push({ id: 'watchbetter-embed', type: 'watchbetter' });
    acc.tablet.push({ id: 'watchbetter-embed', type: 'watchbetter' });
    acc.desktop.push({ id: 'watchbetter-embed', type: 'watchbetter' });
  }

  return acc;
};
