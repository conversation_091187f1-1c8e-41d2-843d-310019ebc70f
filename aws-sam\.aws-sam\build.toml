# This file is auto generated by SAM CLI build command

[function_build_definitions.a43ab488-b4a2-4467-9a0a-8454d4a4c436]
codeuri = "E:\\Github\\aider\\ad-checker\\aws-sam\\functions\\check-ads-for-url"
runtime = "nodejs22.x"
architecture = "x86_64"
handler = "app.lambdaHandler"
manifest_hash = ""
packagetype = "Zip"
functions = ["CheckAdsForURL"]

[layer_build_definitions.6fdb1bc7-0046-4027-9964-f23110518f60]
layer_name = "ChromiumLayer"
codeuri = "E:\\Github\\aider\\ad-checker\\aws-sam\\layers\\chromium"
build_method = "nodejs22.x"
compatible_runtimes = ["nodejs22.x"]
architecture = "x86_64"
manifest_hash = ""
layer = "ChromiumLayer"
