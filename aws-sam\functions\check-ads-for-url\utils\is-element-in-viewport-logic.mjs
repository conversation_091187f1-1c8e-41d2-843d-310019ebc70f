// this  function is used for debugging purposes
export const isElementInViewportLogic = (el) => {
  if (!el) return false;
  const rect = el.getBoundingClientRect();
  const vHeight = window.innerHeight || document.documentElement.clientHeight;
  const vWidth = window.innerWidth || document.documentElement.clientWidth;

  // Checks whether any part of the element is visible in the viewport.
  const vertInView = rect.top < vHeight && rect.bottom > 0;
  const horInView = rect.left < vWidth && rect.right > 0;

  return vertInView && horInView;
};
