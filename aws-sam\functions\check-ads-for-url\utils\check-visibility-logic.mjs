// --- Logic for Layer 3: Visibility Check (executed in the browser context) ---
export const checkVisibilityLogic = (selector) => {
  const el = document.querySelector(selector);
  if (!el) {
    return [
      {
        id: selector,
        isWithinPageBounds: false,
        isVisibleByCss: false,
        isOccluded: false,
        isPartiallyOutOfViewport: true,
        visibilityPercentage: 0,
        occludingElements: [],
        reason: 'Element not found',
      },
    ];
  }

  const pageScrollHeight = document.body.scrollHeight;
  const pageScrollWidth = document.body.scrollWidth;
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;

  const qmnContainer = el.closest(
    'div[id^="qmn"], div[id="watchbetter-embed"]'
  );
  const parentOfQmnContainer = qmnContainer ? qmnContainer.parentElement : null;

  const rect = el.getBoundingClientRect();

  const isRendered = rect.width > 0 && rect.height > 0;

  const scrollTop = window.scrollY || window.pageYOffset;
  const scrollLeft = window.scrollX || window.pageXOffset;
  const isWithinPageBounds =
    isRendered &&
    rect.top + scrollTop >= 0 &&
    rect.left + scrollLeft >= 0 &&
    rect.bottom + scrollTop <= pageScrollHeight &&
    rect.right + scrollLeft <= pageScrollWidth;

  let isVisibleByCss = false;
  if (isRendered) {
    if (typeof el.checkVisibility === 'function') {
      isVisibleByCss = el.checkVisibility({
        checkOpacity: true,
        checkVisibilityCSS: true,
      });
    }
  }

  let visiblePoints = 0;
  const occludingElementsRaw = [];
  let pointsOutsideViewport = 0;

    // Generate grid-based sample points
  const generateSamplePoints = (rect, rows, cols) => {
    const points = [];

    //we can define a margin to exclude points that are too close to the border
    const marginX = 0; 
    const marginY = 0;
    
    const usableWidth = rect.width - (2 * marginX);
    const usableHeight = rect.height - (2 * marginY);
    
    const stepX = cols > 1 ? usableWidth / (cols - 1) : 0;
    const stepY = rows > 1 ? usableHeight / (rows - 1) : 0;
    
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const x = rect.left + marginX + (col * stepX);
        const y = rect.top + marginY + (row * stepY);
        points.push({ x: Math.round(x), y: Math.round(y) });
      }
    }
    
    return points;
  };

  const samplePoints = generateSamplePoints(rect, 32, 32);

  if (isVisibleByCss && isWithinPageBounds) {
    samplePoints.forEach((point) => {
      if (
        point.x >= 0 &&
        point.x < viewportWidth &&
        point.y >= 0 &&
        point.y < viewportHeight
      ) {
        const elementAtPoint = document.elementFromPoint(point.x, point.y);

        if (!elementAtPoint) {
          return; // Not visible at this point
        }

        if (
          el.contains(elementAtPoint) ||
          (qmnContainer && elementAtPoint === qmnContainer) ||
          (parentOfQmnContainer && elementAtPoint === parentOfQmnContainer)
        ) {
          visiblePoints++;
        } else {
          // Occluded
          occludingElementsRaw.push({
            tag: elementAtPoint.tagName,
            id: elementAtPoint.id || null,
            className: elementAtPoint.className || null,
          });
        }
      } else {
        pointsOutsideViewport++;
      }
    });
  }

  const visibilityPercentage = Math.round(
    (visiblePoints / samplePoints.length) * 100
  );

  // Deduplicate occluding elements
  const occludingElements = [];
  if (occludingElementsRaw.length > 0) {
    const seenOccluders = new Set();
    for (const o of occludingElementsRaw) {
      const key = `${o.tag}${o.id ? '#' + o.id : ''}${o.className ? '.' + String(o.className).split(' ').join('.') : ''}`;
      if (!seenOccluders.has(key)) {
        seenOccluders.add(key);
        occludingElements.push(o);
      }
    }
  }

  const isOccluded = occludingElements.length > 0;
  const isPartiallyOutOfViewport = pointsOutsideViewport > 0;

  const finalResult = {
    id: el.id,
    isWithinPageBounds,
    isVisibleByCss,
    isOccluded,
    isPartiallyOutOfViewport,
    visibilityPercentage,
    occludingElements,
  };

  return [finalResult];
};
