import { getElementId } from './get-element-id.mjs';
import { checkVisibilityLogic } from './check-visibility-logic.mjs';
import { isElementInViewportLogic } from './is-element-in-viewport-logic.mjs';
import { logger } from './logger.mjs';

export async function runVisibilityCheck(
  page,
  adsToCheck,
  deviceName,
  responsiveKey,
  issues
) {
  if (adsToCheck.length === 0) {
    logger.silly(
      `### ➡️ [${deviceName}] SUCCESS [VISIBILITY]: No ads to check for this layer.`
    );
    console.log(
      `### ➡️ [${deviceName}] SUCCESS [VISIBILITY]: No ads to check for this layer.`
    );
    return 0; // there is nothing to check, so we return 0 issues.
  }

  const notVisibleAds = [];
  logger.silly(
    `### ➡️ [${deviceName}] [VISIBILITY] Starting individual visibility check for ${adsToCheck.length} ads...`
  );
  console.log(
    `### ➡️ [${deviceName}] [VISIBILITY] Starting individual visibility check for ${adsToCheck.length} ads...`
  );

  for (const ad of adsToCheck) {
    let iframeHandle = null;

    // parentContainerHandle saves the parent container of the iframe. It is saved because it is part of the exclusion list.
    // the exclusion list is a list of elements that are not considered as possible occluder when checking if the ad is occluded
    let parentContainerHandle = null;
    let parentContainerId = null;

    try {
      if (ad.type === 'watchbetter') {
        // whatchbetter has a different structure.
        // --- Specific logic for watchbetter ---
        logger.silly(
          `### ➡️ [${deviceName}] ➡️ Checking ad ID: ${ad.id} (watchbetter). Searching for container: #${ad.id}`
        );
        console.log(
          `### ➡️ [${deviceName}] [VISIBILITY] ➡️ Checking ad ID: ${ad.id} (watchbetter). Searching for container: #${ad.id}`
        );
        console.time(`⏳ wait for watchbetter container`);
        const watchbetterContainerHandle = await page.$(`#${ad.id}`);
        if (watchbetterContainerHandle) {
          parentContainerId = await getElementId(watchbetterContainerHandle);
          iframeHandle = await watchbetterContainerHandle.waitForSelector('iframe', { timeout: 5000, visible: true });
          if (iframeHandle) {
            parentContainerHandle = watchbetterContainerHandle;
            logger.silly(
              `### ➡️ [${deviceName}] ✅ Found iframe inside #${parentContainerId} for watchbetter ad.`
            );
            console.log(
              `### ➡️ [${deviceName}] [VISIBILITY] ✅ Found iframe inside #${parentContainerId} for watchbetter ad.`
            );
          }
        }
        console.timeEnd(`⏳ wait for watchbetter container`);
      } else {
        // --- New logic for QMN ads ---
        const adContainerSelector = `#qmn${ad.id}`;
        console.log(`  [${deviceName}] 📦 Prüfe Ad Container: ${adContainerSelector}`);


        try {
            // Step 1: Wait for the ad container to be present on the page
            const containerHandle = await page.waitForSelector(adContainerSelector, { timeout: 10000 }); // Increased timeout for container
            
            if (!containerHandle) {
                const details = { isTrulyVisible: false, reason: "Ad container element not found (timeout)." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`[${deviceName}]   - ❌ ${adContainerSelector}: Ad-Container nicht gefunden oder Timeout.`);
                continue;
            }

            // Step 2: Look for an iframe *inside* that specific ad container
            iframeHandle = await containerHandle.waitForSelector('iframe[id^="adspiritflash"]', { timeout: 10000 }); // Wait for iframe inside container

            if (!iframeHandle) {
                const details = { isTrulyVisible: false, reason: "Iframe element not found within specific ad container (timeout)." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`[${deviceName}]   - ❌ ${adContainerSelector} iframe: Nicht gefunden (Timeout oder Strukturabweichung).`);
                await containerHandle.dispose(); // Clean up container handle
                continue;
            }
            
            // Set parent container for exclusion list
            parentContainerHandle = containerHandle;
            parentContainerId = await getElementId(containerHandle);
            
        } catch (error) {
            const details = {
                isTrulyVisible: false,
                reason: `Error during iframe discovery: ${error.message}`,
            };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`[${deviceName}]   - ❌ Error finding ${adContainerSelector}: ${error.message}`);
            continue;
        }
      }
    } catch (error) {
      logger.silly(
        `### ➡️ [${deviceName}] ❌ ERROR during iframe discovery for ad.id ${ad.id}: ${error.message}`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] ❌ ERROR during iframe discovery for ad.id ${ad.id}: ${error.message}`
      );
      const details = {
        isTrulyVisible: false,
        reason: `Error during iframe discovery: ${error.message}`,
      };
      notVisibleAds.push({ ...ad, visibilityDetails: details });
      continue;
    }

    // --- Common logic after the iframe search ---
    if (!iframeHandle) {
      const details = {
        isTrulyVisible: false,
        reason: 'Iframe element not found after hierarchical search.',
      };
      notVisibleAds.push({ ...ad, visibilityDetails: details });
      logger.silly(
        `### ➡️ [${deviceName}] - Ad ID ${ad.id}: Iframe not found after all attempts.`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - Ad ID ${ad.id}: Iframe not found after all attempts.`
      );
      if (parentContainerHandle) await parentContainerHandle.dispose();
      continue;
    }

    let adSelector;
    console.time(`⏳ wait for iframe id`);
    if (ad.type === 'watchbetter') {
      // For watchbetter, the iframe has no ID. We build a selector based on its parent.
      // The parentContainerId was already found in the try-block.
      adSelector = `#${parentContainerId} > iframe`;
      logger.silly(
        `### ➡️ [${deviceName}] - Using selector for watchbetter ad: ${adSelector}`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - Using selector for watchbetter ad: ${adSelector}`
      );
    } else {
      // For all other ads, we expect an ID on the iframe.
      const iframeId = await iframeHandle.evaluate((iframe) => iframe.id);
      logger.silly(
        `### ➡️ [${deviceName}] - Iframe ID found for Ad ID ${ad.id}: ${iframeId}`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - Iframe ID found for Ad ID ${ad.id}: ${iframeId}`
      );

      if (!iframeId) {
        const details = {
          isTrulyVisible: false,
          reason:
            'Iframe element found, but it has no ID, so it cannot be checked for visibility.',
        };
        notVisibleAds.push({ ...ad, visibilityDetails: details });
        logger.silly(
          `### ➡️ [${deviceName}] - Ad ID ${ad.id}: Iframe found, but it has no ID.`
        );
        console.log(
          `### ➡️ [${deviceName}] [VISIBILITY] - Ad ID ${ad.id}: Iframe found, but it has no ID.`
        );
        if (parentContainerHandle) await parentContainerHandle.dispose();
        await iframeHandle.dispose();
        continue;
      }
      adSelector = `#${iframeId}`;
    }
    console.timeEnd(`⏳ wait for iframe id`);

    // --- Ad-Type-Specific Visibility Logic ---
    logger.silly(
      `### ➡️ [${deviceName}] - Applying visibility logic for ad type: ${ad.type}`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - Applying visibility logic for ad type: ${ad.type}`
    );

    let floorAdButtonHandle = null; // Handle for the floorad button

  
    switch (ad.type) {
      case 'floorad': {

        console.time(`⏳ wait for floorad button`);
        logger.silly(
          `### ➡️ [${deviceName}] Specific logic for 'floorad' is applied.`
        );
        console.log(
          `### ➡️ [${deviceName}] [VISIBILITY] - Specific logic for 'floorad' is applied.`
        );
        const floorAdButtonSelector = 'div[id^="mflbuttons-"]';
        floorAdButtonHandle = await page.$(floorAdButtonSelector);

        if (floorAdButtonHandle) {
          logger.silly(
            `### ➡️ [${deviceName}] Floorad button found. Click to open the ad.`
          );
          console.log(
            `### ➡️ [${deviceName}] [VISIBILITY] - Floorad button found. Click to open the ad.`
          );
          await floorAdButtonHandle.click();
          await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for animation
        } else {
          logger.silly(
            `### ➡️ [${deviceName}] WARN: Floorad button (${floorAdButtonSelector}) not found.`
          );
          console.log(
            `### ➡️ [${deviceName}] [VISIBILITY] - WARN: Floorad button (${floorAdButtonSelector}) not found.`
          );
        }
        console.timeEnd(`⏳ wait for floorad button`);
        break;
        
      }

      case 'sitebar': {
        console.time(`⏳ wait for scroll sitebar`);
        logger.silly(
          `### ➡️ [${deviceName}] Specific logic for 'sitebar' is applied.`
        );
        console.log(
          `### ➡️ [${deviceName}] [VISIBILITY] - Specific logic for 'sitebar' is applied.`
        );
        if (!parentContainerHandle) {
          logger.silly(
            `### ➡️ [${deviceName}] WARN: Parent container handle for sitebar ad ${ad.id} not found. Scroll logic skipped.`
          );
          console.log(
            `### ➡️ [${deviceName}] [VISIBILITY] - WARN: Parent container handle for sitebar ad ${ad.id} not found. Scroll logic skipped.`
          );
          break;
        }

        let isContainerVisibleByCss = await parentContainerHandle.evaluate(
          (el) => {
            return el.checkVisibility({
              checkOpacity: true,
              checkVisibilityCSS: true,
            });
          }
        );

        logger.silly(
          `### ➡️ [${deviceName}] Initial CSS visibility check for Sitebar container ad ${ad.id}: ${isContainerVisibleByCss}`
        );
        console.log(
          `### ➡️ [${deviceName}] [VISIBILITY] - Initial CSS visibility check for Sitebar container ad ${ad.id}: ${isContainerVisibleByCss}`
        );

        if (isContainerVisibleByCss) {
          logger.silly(
            `### ➡️ [${deviceName}] Sitebar container for ad ${ad.id} is already visible via CSS.`
          );
          console.log(
            `### ➡️ [${deviceName}] [VISIBILITY] - Sitebar container for ad ${ad.id} is already visible via CSS.`
          )
        } else {
          logger.silly(
            `### ➡️ [${deviceName}] Sitebar container for ad ${ad.id} is not visible via CSS. Trying to make it visible by scrolling.`
          );
          console.log(
            `### ➡️ [${deviceName}] [VISIBILITY] - Sitebar container for ad ${ad.id} is not visible via CSS. Trying to make it visible by scrolling.`
          );

          const maxScrollAttempts = 50; // Maximum number of scroll attempts
          let attempts = 0;

          const scrollPosition = await page.evaluate(() => ({
            scrollTop: window.scrollY,
            scrollHeight: document.body.scrollHeight,
            clientHeight: document.documentElement.clientHeight,
          }));

          const isCloseToBottom =
            scrollPosition.scrollTop + scrollPosition.clientHeight >=
            scrollPosition.scrollHeight - 100;
          const scrollDirection = isCloseToBottom ? 1 : -1; // -1 for up, 1 for down
          const scrollAmount = 120; // Pixels per attempt

          logger.silly(`### ${isCloseToBottom}`);
          console.log(`### ${isCloseToBottom}`);

          logger.silly(
            `### ➡️ [${deviceName}] - Scroll ${scrollDirection === 1 ? 'DOWN' : 'UP'}, to find the sitebar container.`
          );
          console.log(
            `### ➡️ [${deviceName}] [VISIBILITY] - Scroll ${scrollDirection === 1 ? 'DOWN' : 'UP'}, to find the sitebar container.`
          );

          while (!isContainerVisibleByCss && attempts < maxScrollAttempts) {
            await page.evaluate((y) => {
              window.scrollBy(0, y);
            }, scrollDirection * scrollAmount);
            await new Promise((resolve) => setTimeout(resolve, 100));

            isContainerVisibleByCss = await parentContainerHandle.evaluate(
              (el) => {
                if (typeof el.checkVisibility !== 'function') return false;
                return el.checkVisibility({
                  checkOpacity: true,
                  checkVisibilityCSS: true,
                });
              }
            );
            attempts++;

            const newScrollTop = await page.evaluate(() => window.scrollY);
            if (
              (scrollDirection === -1 && newScrollTop === 0) ||
              (scrollDirection === 1 &&
                (await page.evaluate(
                  () =>
                    window.innerHeight + window.scrollY >=
                    document.body.scrollHeight
                )))
            ) {
              logger.silly(
                `### ➡️ [${deviceName}] - page ${scrollDirection === 1 ? 'end' : 'start'} Reached. Stop the scroll search.`
              );
              console.log(
                `### ➡️ [${deviceName}] [VISIBILITY] - page ${scrollDirection === 1 ? 'end' : 'start'} Reached. Stop the scroll search.`
              );
              break;
            }
          }

          if (isContainerVisibleByCss) {
            logger.silly(
              `### ➡️ [${deviceName}] - Sitebar container is now visible after ${attempts} scroll attempts via CSS.`
            );
            console.log(
              `### ➡️ [${deviceName}] [VISIBILITY] - Sitebar container is now visible after ${attempts} scroll attempts via CSS.`
            );
          } else {
            logger.silly(
              `### ➡️ [${deviceName}] - WARN: Could not make the sitebar container visible after ${attempts} attempts via CSS.`
            );
            console.log(
              `### ➡️ [${deviceName}] [VISIBILITY] - WARN: Could not make the sitebar container visible after ${attempts} attempts via CSS.`
            );
          }
        }

        await new Promise((resolve) => setTimeout(resolve, 500));

        console.timeEnd(`⏳ wait for scroll sitebar`);
        break;
      }

      
      case 'intext':
      case 'prospekt':
      case 'hpa':
      default: // Standard logic for known and unknown types
       console.time(`⏳ wait for scroll into view`);
        logger.silly(
          `### ➡️ [${deviceName}] - Standard logic (scrollIntoView) is applied.`
        );
        console.log(
          `### ➡️ [${deviceName}] [VISIBILITY] - Standard logic (scrollIntoView) is applied.`
        );
        // Scroll the element into the center of the viewport, to ensure it is checkable.
        await iframeHandle.evaluate((el) => {
          const rect = el.getBoundingClientRect();
          const viewportHeight =
            window.innerHeight || document.documentElement.clientHeight;
          // Calculate the target scroll position to place the element 200px above the center
          const targetY =
            rect.top + window.scrollY - viewportHeight / 2 + rect.height / 2;
          window.scrollTo({ top: targetY });
        });
        // A short wait can help stabilize the page before scrolling.
        await new Promise((resolve) => setTimeout(resolve, 500));
        console.timeEnd(`⏳ wait for scroll into view`);
        break;
    }

    // DEBUG: Check if the element is in the viewport after the action.
    const isElementInViewportForDebug = await iframeHandle.evaluate(
      isElementInViewportLogic
    );
    logger.silly(
      `### ➡️ [${deviceName}] - DEBUG: Element ${adSelector} in viewport after action: ${isElementInViewportForDebug}`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - DEBUG: Element ${adSelector} in viewport after action: ${isElementInViewportForDebug}`
    );

    // Evaluate the visibility of this single, now centered element.
    const visibilityResults = await page.evaluate(
      checkVisibilityLogic,
      adSelector
    );
    const result = visibilityResults[0];

    if (!result || result.visibilityPercentage < 100) {
      let reason = 'Others'; // Default reason

      if (result.reason === 'Element not found') {
        reason = 'Element not found';
      } else if (!result.isVisibleByCss) {
        reason = 'Hidden by CSS';
      } else if (result.isOccluded) {
        reason = 'Covered';
      } else if (result.isPartiallyOutOfViewport) {
        reason = 'Not in viewport';
      } else if (!result.isWithinPageBounds) {
        reason = 'Not within page bounds';
      }

      const details = {
        visibilityPercentage: result.visibilityPercentage,
        reason: reason,
        isVisibleByCss: result.isVisibleByCss,
        isOccluded: result.isOccluded,
        isPartiallyOutOfViewport: result.isPartiallyOutOfViewport,
        isWithinPageBounds: result.isWithinPageBounds,
        occludingElements: result.occludingElements,
      };

      notVisibleAds.push({
        id: ad.id,
        type: ad.type,
        visibilityDetails: details,
      });
      logger.silly(
        `### ➡️ [${deviceName}] - ❌ ${adSelector}: Not visible. Reason: ${details.reason}, Visibility: ${details.visibilityPercentage}%`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - ❌ ${adSelector}: Not visible. Reason: ${details.reason}, Visibility: ${details.visibilityPercentage}%`
      );
    } else {
      logger.silly(
        `### ➡️ [${deviceName}] - ✅ ${adSelector}: Visible. (Visibility: ${result.visibilityPercentage}%)`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - ✅ ${adSelector}: Visible. (Visibility: ${result.visibilityPercentage}%)`
      );
    }

    // Close the Floorad ad if it was opened
    if (floorAdButtonHandle) {
      logger.silly(
        `### ➡️ [${deviceName}] Close Floorad ad by clicking again.`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - Close Floorad ad by clicking again.`
      );
      try {
        await floorAdButtonHandle.click();
        await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for animation
      } catch (e) {
        logger.silly(
          `### ➡️ [${deviceName}] WARN: Floorad button could not be clicked again (possibly already closed): ${e.message}`
        );
        console.log(
          `### ➡️ [${deviceName}] [VISIBILITY] - WARN: Floorad button could not be clicked again (possibly already closed): ${e.message}`
        );
      } finally {
        await floorAdButtonHandle.dispose();
      }
    }

    if (parentContainerHandle) {
      await parentContainerHandle.dispose();
    }
    await iframeHandle.dispose(); // Clean up the iframe handle
  }

  if (notVisibleAds.length > 0) {
    if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
    issues.visibility[responsiveKey] = notVisibleAds;
    logger.silly(
      `[${deviceName}] ERROR [Visibility]: ${notVisibleAds.length} of ${adsToCheck.length} ads not really visible.`
    );
    console.log(
      `[${deviceName}] ERROR [Visibility]: ${notVisibleAds.length} of ${adsToCheck.length} ads not really visible.`
    );
  } else {
    logger.silly(
      `[${deviceName}] SUCCESS [Visibility]: All ${adsToCheck.length} ads are visible.`
    );
    console.log(
      `[${deviceName}] SUCCESS [Visibility]: All ${adsToCheck.length} ads are visible.`
    );
  }
  return notVisibleAds.length;
}
