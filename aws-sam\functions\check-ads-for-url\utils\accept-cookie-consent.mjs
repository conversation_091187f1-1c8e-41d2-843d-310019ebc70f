import { logger } from './logger.mjs';

export const acceptCookieConsent = async (page) => {
  try {
    // Wait for the container that hosts the shadow root
    await page.waitForSelector('#cmpwrapper', { timeout: 5000 });

    const shadowHost = await page.$('#cmpwrapper');
    const shadowRootHandle = await shadowHost.evaluateHandle(
      (el) => el.shadowRoot
    );
    if (!shadowRootHandle) return -2;

    // Try to find the button and wait for it to be available
    const acceptButtonHandle = await page.waitForFunction(
      (shadowRoot) => shadowRoot.querySelector('.cmptxt_btn_yes'),
      { timeout: 5000 },
      shadowRootHandle
    );

    if (!acceptButtonHandle) {
      logger.silly('❌ Consent button not found in shadow root');
      return -3;
    }
    const button = acceptButtonHandle.asElement();

    if (button) {
      await button.click();
      logger.silly('✅ Consent button clicked');
      return 1;
    } else {
      logger.silly('❌ Consent button not found as element');
      return 0;
    }
  } catch (err) {
    logger.silly('⚠️ Error during cookie consent:', err.message);
    return -1;
  }
};
