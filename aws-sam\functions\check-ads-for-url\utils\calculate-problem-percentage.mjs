
export default function calculateProblemPercentage(flags=[], adsByResponsive={}) {

  // Calculate total number of ads across all devices
  let totalAds = 0;
  for (const [, ads] of Object.entries(adsByResponsive)) {
    totalAds += ads.length;
  }
  
  if (totalAds === 0) {
    return 0;
  }

  const adsWithIssues = new Set();
  
  flags.forEach(flag => {
    // Only consider flags with severity RED, ORANGE, or YELLOW as issues
    if (flag.severity && ['RED', 'ORANGE', 'YELLOW'].includes(flag.severity) &&
        flag.context && flag.context.device && flag.context.responsive && flag.context.adId) {
      // Create unique identifier: device-responsive-adId
      const adIdentifier = `${flag.context.device}-${flag.context.responsive}-${flag.context.adId}`;
      adsWithIssues.add(adIdentifier);
    }
  });
  
  const problemPercentage = (adsWithIssues.size / totalAds) * 100;
  return Math.round(problemPercentage * 100) / 100;
}
