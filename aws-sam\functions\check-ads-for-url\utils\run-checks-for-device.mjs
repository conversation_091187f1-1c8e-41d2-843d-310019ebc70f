import { acceptCookieConsent } from './accept-cookie-consent.mjs';
import { autoScrollToBottom } from './auto-scroll-to-bottom.mjs';
import { checkSelectors } from './check-selectors.mjs';
import { groupAdsByResponsive } from './group-ads-by-responsive.mjs';
import { runPlacementCheck } from './run-placement-check.mjs';
import { runVisibilityCheck } from './run-visibility-check.mjs';
import { logger } from './logger.mjs';

export async function runChecksForDevice(
  browser,
  url,
  deviceName,
  config,
  activeLayers,
  consentState
) {
  const page = await browser.newPage();
  page.on('console', (msg) => {
    logger.silly(`[${config.name} BROWSER LOG]: ${msg.text()}`);
    console.log(`[${config.name} BROWSER LOG]: ${msg.text()}`)
});

  try {
    logger.silly(
      `\n--- Test device: ${config.name} (${config.width}x${config.height}) ---`
    );
    console.log(`
      \n--- Test device: ${config.name} (${config.width}x${config.height}) ---
      `)

    console.time(`⏳ navigate to url`);

    await page.setViewport({ width: config.width, height: config.height });
    await page.goto(url, { waitUntil: 'networkidle2' });

    /*
    hr
{
        waitUntil: ['load', 'domcontentloaded', 'networkidle0'],
        timeout: 30000,
      }
  */

    console.timeEnd(`⏳ navigate to url`);

    console.time(`⏳ accept cookie consent`);
    if (!consentState.handled) {
      logger.silly(
        `[${config.name}] First device check: Trying to accept cookie consent...`
      );
      console.log(`[${config.name}] First device check: Trying to accept cookie consent...`)
      const consentResult = await acceptCookieConsent(page);
      if (consentResult === 1) {
        logger.silly(`[${config.name}] Cookie consent accepted successfully.`);
        console.log(`[${config.name}] Cookie consent accepted successfully.`)
        consentState.handled = true;
      } else {
        throw new Error(
          `[${config.name}] Cookie consent could not be accepted (Code: ${consentResult}).`
        );
      }
    }
    console.timeEnd(`⏳ accept cookie consent`);

    logger.silly(`[${config.name}] Wait for TCF API confirmation... ⏳ `);
    console.log(`[${config.name}] Wait for TCF API confirmation... ⏳ `);

    console.time(`⏳ wait for TCF API`);
    try {
      await page.evaluate(() => {
        return new Promise((resolve, reject) => {
          // Retry mechanism to wait for the TCF API to be ready and successful
          const checkTcfApi = (retries = 3) => {
            // ~1.5 seconds timeout (3 * 500ms)
            if (typeof window.__tcfapi === 'function') {
              window.__tcfapi('getTCData', 2, (tcData, success) => {
                if (success) {
                  // Can't use console from node here, so log to browser console
                  /* console.log('TCF API success:', tcData); */
                  console.log('TCF API success: ✅ ');
                  resolve();
                } else if (retries > 0) {
                  setTimeout(() => checkTcfApi(retries - 1), 500);
                } else {
                  reject(
                    new Error(
                      'TCF API did not return success within the timeout.'
                    )
                  );
                }
              });
            } else if (retries > 0) {
              setTimeout(() => checkTcfApi(retries - 1), 500);
            } else {
              reject(
                new Error(
                  '__tcfapi function not found on page within the timeout. ❌ '
                )
              );
            }
          };
          checkTcfApi();
        });
      });
      logger.silly(
        `[${config.name}] TCF API confirmation received successfully. ✅ `
      );
      console.log(`[${config.name}] TCF API confirmation received successfully. ✅ `)
    } catch (e) {
      logger.silly(
        `[${config.name}] Error waiting for TCF API: ${e.message} ❌ `
      );
      console.log(`[${config.name}] Error waiting for TCF API: ${e.message} ❌ `)
      throw new Error(`[${config.name}] TCF validation failed: ${e.message}`);
    }
    console.timeEnd(`⏳ wait for TCF API`);

    // Scroll the page to the bottom to ensure all content has been loaded.

    // A short wait can help stabilize the page before scrolling.
    await new Promise((resolve) => setTimeout(resolve, 500));

    console.time(`⏳ auto scroll to bottom`);
    // lazy load to ensure all content has been loaded before checking the ads.
    await autoScrollToBottom(page);

    console.timeEnd(`⏳ auto scroll to bottom`);

    try {
      const pageHtml = await page.content(); // full HTML of the loaded page
      console.log(`\n[${config.name}] PAGE HTML (length: ${pageHtml.length}):\n`, pageHtml);
    } catch (err) {
      console.log(`[${config.name}] Could not capture page HTML: ${err.message}`);
    }

    console.time(`⏳ check selectors`);
    // we extract the ads from the QMN object
    const qmnConfig = await checkSelectors(page);

    console.timeEnd(`⏳ check selectors`);

    if (!qmnConfig) {
      console.log(`🇦🇩 ❌ [${config.name}] [QMN CONFIG] Could not retrieve window.qmn.config object.`)
      throw new Error(
        `[${config.name}] Could not retrieve window.qmn.config object.`
      );
    }

    console.log(`
    🔧[${config.name}] [QMN CONFIG] QMN config:`, JSON.stringify(qmnConfig, null, 2)
    )


    // we group the ads by responsive type (desktop/mobile)
    let adsByResponsive = {};
    adsByResponsive = groupAdsByResponsive(qmnConfig);

    console.log(`
      🗂️[${config.name}] [GROUP ADS BY RESPONSIVE] Ads by responsive:`, JSON.stringify(adsByResponsive, null, 2)
    )
    const responsiveKey = config.deviceType;
    const adsToCheck = adsByResponsive[responsiveKey] || [];
    logger.silly(
      `[${deviceName}] Check ${adsToCheck.length} ads for responsive type '${responsiveKey}'.`
    );
    console.log(
      `🗂️[${deviceName}] [GROUP ADS BY RESPONSIVE] Check ${adsToCheck.length} ads for responsive type '${responsiveKey}'.`
    )

    // we run the checks for each layer (placement, visibility) and count the issues.
    const issues = {};
    let issueCount = 0;

    if (adsToCheck.length > 0) {
      if (activeLayers.includes('placement')) {

        console.time(`⏳ run placement check`);
        issueCount += await runPlacementCheck(
          page,
          adsToCheck,
          deviceName,
          responsiveKey,
          issues
        );
        console.timeEnd(`⏳ run placement check`);
      }

      if (activeLayers.includes('visibility')) {
        console.time(`⏳ run visibility check`);
        issueCount += await runVisibilityCheck(
          page,
          adsToCheck,
          deviceName,
          responsiveKey,
          issues
        );
        console.timeEnd(`⏳ run visibility check`);
      }
    } else {
      logger.silly(
        `[${deviceName}] No ads to check for device type '${responsiveKey}'.`
      );
      console.log(
        ` 🗂️[${deviceName}] [GROUP ADS BY RESPONSIVE] No ads to check for device type '${responsiveKey}'.`
      );
    }

    return { deviceName, responsiveKey, issues, issueCount, error: null, adsByResponsive };
  } catch (e) {
    logger.silly(`❌ Error with device ${deviceName}:`, e.message);
    console.log(`❌ Error with device ${deviceName}:`, e.message);
    return { deviceName, error: e.message, issues: {}, issueCount: 1 };
  } finally {
    await page.close();
  }
}
