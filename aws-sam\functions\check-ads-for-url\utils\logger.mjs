import { createLogger, format, transports } from 'winston';
import { formatDate } from 'date-fns';

const isProduction = process.env.NODE_ENV === 'production';
/*
  Log Levels (from least to most severe):

  * silly
  * debug <-- development
  * verbose
  * info <-- production
  * warn
  * error
 */
const logLevel = isProduction ? 'info' : 'debug';

const myFormat = format.printf(
  ({ level, message, jobId, timestamp, articleId }) => {
    let result = `${level}: ${message}`;

    if (jobId) {
      result = `id(${jobId}) ${result}`;
    }

    if (timestamp) {
      result = `${formatDate(timestamp, 'dd.MM.yyyy HH:mm:ss')} ${result}`;
    }

    if (articleId) {
      result += ` { article: ${articleId} }`;
    }

    return result;
  }
);

const developFormat = format.combine(
  format.timestamp(),
  format.colorize(),
  format.simple(),
  myFormat
);

const productionFormat = format.combine(
  format.colorize(),
  format.simple(),
  myFormat
);

export const logger = createLogger({
  level: logLevel,
  format: isProduction ? productionFormat : developFormat,
  transports: [new transports.Console()],
});
