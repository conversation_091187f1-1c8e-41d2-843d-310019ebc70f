import chromium from '@sparticuz/chromium';
import puppeteer from 'puppeteer-core';
import { checkAdsForUrl } from './utils/check-ads-for-url.mjs';

export const lambdaHandler = async (event) => {
  try {
    const requestBody = event.body;
    const bodyObject = JSON.parse(requestBody);
    const url = bodyObject.url;
    const articleId = bodyObject.articleId;
    const layers = bodyObject.layers;

    console.log(`------------------------`);
    console.log('Received event:');
    console.log(`🔗 URL: ${url}`);
    console.log(`🆔 Article ID: ${articleId}`);
    console.log(`🎚️ layer: ${layers}`);
    console.log(`------------------------`);
    
    if (!url) {
      return { success: false, error: 'Missing parameter: url' };
    }

    if (!articleId) {
      return { success: false, error: 'Missing parameter: articleId' };
    }

    const browser = await puppeteer.launch({
      args: chromium.args,
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless,
    });

    const result = await checkAdsForUrl(browser, { url, articleId, layers });

    return { success: true, result };
  } catch (e) {
    return {
      success: false,
      error: e.message,
    };
  }
};
